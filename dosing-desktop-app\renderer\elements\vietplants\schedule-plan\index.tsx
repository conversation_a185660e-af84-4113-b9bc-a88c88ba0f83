import { FC, useEffect, useRef, useState } from "react";
import useSchedulePlanStore, {
  ScheduleAction,
  ScheduleProgram,
} from "../../../stores/schedulePlanStore";
import useDeviceDataStore from "../../../stores/deviceDataStore";
import { getSchedulePlan } from "../../../services/schedule";
import { Button, Col, Divider, Input, Modal, Row, Select } from "antd";
import SchedulePlanContainer from "./SchedulePlanContainer";
import CreateSchedulePlanOrProgram from "./Create";
import useProgramRunningStore, {
  ProgramRunningFromBroker,
} from "../../../stores/programRunningStore";
import ProgramRunning from "./ProgramRunning";
import { FunctionList } from "../../../services/device/devices";
import dayjs from "dayjs";
import { ClockCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { calculateSecond } from "./ProgramContainer";
import ProgramTriggerImmediately from "./ProgramTriggerImmediately";
import { useMqttStore } from "../../../stores/mqttStore";
import { genDeviceTopic } from "../../../stores/mqttStore.utils";

const SchedulePlan: FC = () => {
  //
  // get schedule plans list
  //
  const { schedulePlans, setSchedulePlans } = useSchedulePlanStore();
  const { deviceId } = useDeviceDataStore();
  useEffect(() => {
    if (!deviceId) return;
    async function getSchedules() {
      const res = await getSchedulePlan(deviceId);
      setSchedulePlans(res?.result?.data || []);
    }
    getSchedules();
  }, [deviceId]);

  const { programRunning, setProgramRunning } = useProgramRunningStore();
  useEffect(() => {
    console.log("programRunning:", programRunning);
  }, [programRunning]);

  const programRunningRef = useRef(programRunning);

  useEffect(() => {
    programRunningRef.current = programRunning;
  }, [programRunning]);

  const { client } = useMqttStore();
  useEffect(() => {
    client.on("message", (topic, message) => {
      if (topic !== genDeviceTopic(deviceId)) return;
      console.log(
        `Received MQTT message on topic ${topic}:`,
        message.toString()
      );
      console.log("data from broker for unchart:", message);
      try {
        const data: Array<{ ts: string; key: string; value: string | number }> =
          JSON.parse(message.toString());
        data.forEach((item: any) => {
          if (item.key === "active_schedule") {
            const itemValue = JSON.parse(item.value);
            console.log("itemValue co neee:", itemValue);
            const checkIfProgramRunningExisted = programRunningRef.current.find(
              (program) => program.scheduleId === itemValue.scheduleId
            );
            if (checkIfProgramRunningExisted) {
              if (itemValue.status === "finished") {
                // console.log("Removing finished program:", itemValue.label);
                const removeProgramRunning = programRunningRef.current.filter(
                  (program) => program.scheduleId !== itemValue.scheduleId
                );
                console.log("removeProgramRunning:", removeProgramRunning);
                setProgramRunning(
                  JSON.parse(
                    JSON.stringify([...removeProgramRunning, itemValue])
                  )
                );
              }
            } else if (itemValue.status === "running") {
              console.log("Adding running program:", itemValue.label);
              setProgramRunning(
                JSON.parse(
                  JSON.stringify([...programRunningRef.current, itemValue])
                )
              );
            }
          }
        });
      } catch (error) {
        console.error("MQTT message error:", error);
      }
    });
  }, [schedulePlans]);

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "start",
        padding: 16,
        gap: 16,
      }}
    >
      <Row gutter={[16, 16]} style={{ width: "100%" }}>
        <Col span={24}>
          <ProgramTriggerImmediately />
        </Col>
      </Row>

      <Divider />

      {programRunning.length > 0 && (
        <div
          style={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "start",
          }}
        >
          <p
            style={{
              fontSize: 24,
              fontWeight: "bold",
              margin: 0,
              marginBottom: 16,
            }}
          >
            Chương trình đang chạy
          </p>
          <Row gutter={[16, 16]} style={{ width: "100%" }}>
            {programRunning.map((program) => (
              <Col span={12} key={program.scheduleId}>
                <ProgramRunning
                  scheduleId={program.scheduleId}
                  label={program.label}
                  status={program.status}
                  start_time={program.start_time}
                  end_time={program.end_time}
                  timestamp={program.timestamp}
                />
              </Col>
            ))}
          </Row>

          <Divider />
        </div>
      )}

      <p style={{ fontSize: 24, fontWeight: "bold", margin: 0 }}>
        Danh sách kế hoạch đã tạo
      </p>
      <Row gutter={[16, 16]} style={{ width: "100%" }}>
        {schedulePlans?.map((plan) => (
          <Col span={12}>
            <SchedulePlanContainer key={plan.name} {...plan} />
          </Col>
        ))}
      </Row>

      <CreateSchedulePlanOrProgram />
    </div>
  );
};

export default SchedulePlan;
