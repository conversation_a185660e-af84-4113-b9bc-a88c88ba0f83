import { Button, Col, Input, message, Modal, Row, Select } from "antd";
import { calculateSecond } from "./ProgramContainer";
import { ClockCircleOutlined, SearchOutlined } from "@ant-design/icons";
import useSchedulePlanStore, {
  ScheduleAction,
  ScheduleProgram,
} from "../../../stores/schedulePlanStore";
import { useEffect, useState } from "react";
import useDeviceDataStore from "../../../stores/deviceDataStore";
import { FunctionList } from "../../../services/device/devices";
import dayjs from "dayjs";
import useControlDevice from "../../../services/device/useControlDevice";

const ProgramTriggerImmediately = () => {
  const { schedulePlans, scheduleProgramTriggerImmediately } =
    useSchedulePlanStore();
  const { deviceId } = useDeviceDataStore();

  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    []
  );
  useEffect(() => {
    if (!scheduleProgramTriggerImmediately) return;
    setOptions(
      scheduleProgramTriggerImmediately.enum_value.split(",").map((item) => ({
        value: item.trim(),
        label: item.trim(),
      }))
    );
  }, [scheduleProgramTriggerImmediately]);

  const [allProgramOptions, setAllProgramOptions] = useState<
    { value: ScheduleProgram; label: string }[]
  >([]);
  const [filteredProgramOptions, setFilteredProgramOptions] = useState<
    { value: ScheduleProgram; label: string }[]
  >([]);
  useEffect(() => {
    if (!schedulePlans) return;

    let tmpOptions = [];
    schedulePlans.forEach((plan) => {
      plan.schedules.forEach((schedule) => {
        tmpOptions.push({ value: schedule, label: schedule.name });
      });
    });
    setAllProgramOptions(tmpOptions);
    // setFilteredProgramOptions(tmpOptions);
  }, [schedulePlans]);

  const [selectedEnv, setSelectedEnv] = useState<string>("");

  const handleSelectEnvironment = (value: string) => {
    setSelectedEnv(value);
    let tmpOptions = allProgramOptions.filter(
      (program) => program.value.action?.env_enum === value
    );
    setFilteredProgramOptions(tmpOptions);
  };

  // const handleSearchProgram = (value: string) => {
  //   if (!value) {
  //     setFilteredProgramOptions(allProgramOptions);
  //     return;
  //   }
  //   let tmpOptions = allProgramOptions.filter((item) =>
  //     item.label.toLowerCase().includes(value)
  //   );
  //   setFilteredProgramOptions(tmpOptions);
  // };

  const { functionListForControl } = useDeviceDataStore();

  const generateActionsOfPlan = (action: ScheduleAction) => {
    const fncControl = functionListForControl.find(
      (item) => item.identifier === "tb1"
    )?.children;

    const lableAndUnit = {
      label: "",
      unit: "",
    };

    const generateActionNameAndUnit = (key: string) => {
      fncControl?.forEach(
        (item: { label: string; children: FunctionList[] }) => {
          item?.children?.forEach((child: FunctionList) => {
            if (child?.identifier === key) {
              lableAndUnit.label = child?.label;
              lableAndUnit.unit = child?.unit;
            }
          });
        }
      );
      return lableAndUnit;
    };

    return (
      <div style={{ width: "100%", display: "flex", flexDirection: "column" }}>
        {Object.entries(action).map(([key, value]) => {
          if (typeof value !== "number" && String(value) !== "true") {
            return null;
          }
          if (typeof value === "number" && value === 0) {
            return null;
          }

          return (
            <div
              key={key}
              style={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                gap: 8,
                borderTop: "1px solid #ddd",
                padding: 4,
              }}
            >
              <p style={{ margin: 0 }}>
                {generateActionNameAndUnit(key).label}
              </p>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  // width: "100%",
                }}
              >
                <p
                  style={{
                    margin: 0,
                    fontWeight: value !== 0 ? "bold" : "normal",
                  }}
                >
                  {value === "false" ? "Tắt" : value === "true" ? "Bật" : value}
                </p>
                <p style={{ margin: 0 }}>{lableAndUnit.unit}</p>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const [selectedProgram, setSelectedProgram] = useState<ScheduleProgram>(null);
  const [openModal, setOpenModal] = useState(false);
  const handleOpenModal = (program: ScheduleProgram) => {
    setSelectedProgram(program);
    setOpenModal(true);
  };
  const handleCloseModal = () => {
    setSelectedProgram(null);
    setOpenModal(false);
  };

  const { run: control, loading } = useControlDevice();

  const handleTriggerProgramImmediately = async () => {
    // const paramsToPush = {
    //   program_id: selectedProgram.id,
    //   program_name: selectedProgram.name,
    //   device_id: deviceId,
    //   type: "trigger_program_immediately",
    //   action: selectedProgram.action,
    // };

    console.log({
      program_id: selectedProgram.id,
      program_name: selectedProgram.name,
      device_id: deviceId,
      type: "trigger_program_immediately",
      action: selectedProgram.action,
    });

    const validActions = Object.fromEntries(
      Object.entries(selectedProgram.action)
        .filter(([key, value]) => {
          return (
            (typeof value === "number" && value !== 0) ||
            String(value) === "true"
          );
        })
        .map(([key, value]) => [
          key,
          String(value) === "true"
            ? true
            : String(value) === "false"
            ? false
            : value,
        ])
    );

    await control({
      device_id_thingsboard: deviceId,
      method: "set_state",
      // params: selectedProgram.action,
      params: { ...validActions, schedule_id: selectedProgram.id },
    }).then((res) => {
      console.log("ket qua gui env_enum: ", res);
      message.success(`Đã gửi yêu cầu kích hoạt chương trình !`);
      handleCloseModal();
    });
  };

  return (
    <div style={{ width: "100%", display: "flex", flexDirection: "column" }}>
      <p style={{ fontWeight: "bold", fontSize: 16 }}>
        Chọn nhanh chương trình
      </p>
      <div
        style={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          gap: 8,
        }}
      >
        <div style={{ display: "flex", flexDirection: "row", gap: 8 }}>
          <Select
            placeholder="Chọn mã môi trường"
            style={{ width: "300px" }}
            onChange={(value) => {
              handleSelectEnvironment(value);
            }}
            options={options}
            allowClear
          />
        </div>
        {/* {
          <Input
            style={{ width: "300px" }}
            addonBefore={<SearchOutlined />}
            placeholder="Tìm kiếm tên chương trình"
            onChange={(value) => handleSearchProgram(value.target.value)}
          />
        } */}
      </div>
      <div
        style={{
          marginTop: 16,
          display: "flex",
          flexDirection: "row",
          gap: 16,
          overflow: "scroll",
        }}
      >
        {filteredProgramOptions.length === 0 && selectedEnv !== "" ? (
          <p style={{ color: "gray" }}>
            Không có chương trình phù hợp với môi trường này !
          </p>
        ) : (
          filteredProgramOptions.map((option) => (
            <div
              key={option.value.id}
              style={{
                cursor: "pointer",
                padding: 8,
                display: "flex",
                flexDirection: "column",
                justifyContent: "flex-start",
                border: "1px solid #ccc",
                borderRadius: 8,
                background: "linear-gradient(to bottom, #fff, 75%, #eee)",
              }}
              className="hoverable"
              onClick={() => handleOpenModal(option.value)}
            >
              <p style={{ fontWeight: "bold" }}>{option.label}</p>
              <p>
                {" "}
                <ClockCircleOutlined style={{ marginRight: 8 }} />
                {dayjs(option.value.start_time, "HH:mm:ss").format(
                  "HH:mm:ss"
                )}{" "}
                - {dayjs(option.value.end_time, "HH:mm:ss").format("HH:mm:ss")}
              </p>
              <div
                style={{
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                }}
              >
                {generateActionsOfPlan(option.value.action)}
              </div>
            </div>
          ))
        )}
        <Modal
          title={`Bạn muốn chạy ngay chương trình ${selectedProgram?.name} ?`}
          open={openModal}
          onOk={() => handleTriggerProgramImmediately()}
          onCancel={() => handleCloseModal()}
          onClose={() => handleCloseModal()}
        >
          <p>
            Chương trình này sẽ chạy trong vòng:{" "}
            {calculateSecond(
              selectedProgram?.start_time,
              selectedProgram?.end_time
            )}{" "}
            Giây
          </p>
        </Modal>
      </div>
    </div>
  );
};

export default ProgramTriggerImmediately;
