import {
  FieldTimeOutlined,
  NotificationOutlined,
  SearchOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";
import { Button, Drawer, Input, message, Popover, Radio } from "antd";
import { FC, useEffect, useState } from "react";
import {
  getUserIdFromToken,
  getWebNotificationListAll,
  NoticeDataRes,
  NoticeIconItemType,
  updateWebNotification,
} from "../../../services/notification";
import Image from "next/image";
import dayjs from "dayjs";
import InputTextWithKeyboard from "../../../components/virtual-input/InputTextWithKeyboard";
import { CheckboxGroupProps } from "antd/es/checkbox";
import { useMqttStore } from "../../../stores/mqttStore";
import { genNoticeUpdateTopic } from "../../../utils/mqtt";
import { getCustomerIdFromToken } from "../../../utils/localStorage";

type NoticeIconItem<T = any> = {
  id?: string;
  extra?: string;
  key?: string;
  read?: boolean;
  avatar?: string;
  title?: string;
  status?: string;
  datetime?: string;
  description?: string;
  type?: NoticeIconItemType;
  data?: T;
};

// type AlarmItemProps = {
//   data: NoticeIconItem<NoticeDataRes>;
// };
// const AlarmItem: FC<AlarmItemProps> = ({ data }) => {
//   const isWarning = data?.data?.type !== "task";
//   return (
//     <div>
//       <div className="flex-none">
//         <Image
//           src={isWarning ? "/images/bell.png" : "/images/calendar.png"}
//           alt="icon"
//           width={28}
//           height={28}
//         />
//       </div>
//       <div className="flex-1 gap-2">
//         <div className="font-semibold">{data.title}</div>
//         <div className="flex items-center justify-between text-gray-400">
//           <div className="flex items-center">
//             <span className="mr-1 ">
//               <FieldTimeOutlined />
//             </span>
//             <span>
//               {dayjs(data.datetime).diff(dayjs(), "day") > 0
//                 ? `${dayjs(data.datetime).diff(dayjs(), "day")} ngày trước`
//                 : `Hôm nay ${dayjs(data.datetime).format("HH:mm")}`}
//             </span>
//           </div>
//           {/* <div className="flex items-center">
//             <span className="mr-1 ">
//               <FieldTimeOutlined />
//             </span>
//             <span>{timeLabel}</span>
//           </div> */}
//         </div>
//       </div>
//     </div>
//   );
// };

type AlarmItemProps = {
  key: any;
  data: NoticeDataRes;
  setNotiData: React.Dispatch<React.SetStateAction<NoticeDataRes[]>>;
  setSearchedNotiData: React.Dispatch<React.SetStateAction<NoticeDataRes[]>>;
  setNotiDataWillBeShown: React.Dispatch<React.SetStateAction<NoticeDataRes[]>>;
  // handleShowSpecificProgramSchedule: (program_id: string) => void;
};
const AlarmItem: FC<AlarmItemProps> = ({
  key,
  data,
  setNotiData,
  setSearchedNotiData,
  setNotiDataWillBeShown,
  // handleShowSpecificProgramSchedule
}) => {
  const isWarning = data?.severity === "notification" ? true : false;
  const { client } = useMqttStore();
  async function pushSeenMsgToBroker(message: {
    name?: string;
    message?: string; // Data
    created_at?: string; // Datetime
    entity?: string; // Data
    type?: "task"; // Data
    is_read?: boolean; // Check
  }) {
    client.publish(
      genNoticeUpdateTopic(),
      JSON.stringify({
        ...message,
        is_read: true,
        customer_user: getCustomerIdFromToken(),
      }),
      (err) => {
        if (err) console.log("err: ", err);
      }
    );
  }

  const handleUpdateNotice = async (item: NoticeDataRes) => {
    try {
      await updateWebNotification({
        name: item.name,
        is_read: true,
      }).then((res) => {
        if (res.statusOK === false) {
          message.error("Có lỗi xảy ra khi đang cập nhật thông báo");
          return;
        }
        pushSeenMsgToBroker({
          name: item.name,
          message: item.message,
          created_at: item.created_at,
          entity: item.entity,
          type: "task",
          is_read: true,
        });
        setNotiData((prev: NoticeDataRes[]) =>
          prev.map((item: NoticeDataRes) => {
            if (item.name === res?.responseData?.result?.data?.name) {
              return { ...item, is_read: true } as NoticeDataRes;
            }
            return item;
          })
        );
        setSearchedNotiData((prev: NoticeDataRes[]) =>
          prev.map((item: NoticeDataRes) => {
            if (item.name === res?.responseData?.result?.data?.name) {
              return { ...item, is_read: true } as NoticeDataRes;
            }
            return item;
          })
        );
        setNotiDataWillBeShown((prev: NoticeDataRes[]) =>
          prev.map((item: NoticeDataRes) => {
            if (item.name === res?.responseData?.result?.data?.name) {
              return { ...item, is_read: true } as NoticeDataRes;
            }
            return item;
          })
        );
      });
      // handleShowSpecificProgramSchedule(item?.);
    } catch {
      message.error("Có lỗi xảy ra khi gửi lệnh cập nhật thông báo");
    }
  };

  return (
    <div
      key={key}
      className="hoverable"
      style={{
        zIndex: 1000,
        cursor: "pointer",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: 16,
        paddingTop: 8,
        paddingBottom: 8,
        color: "rgb(40,40,40)",
      }}
      onClick={() => handleUpdateNotice(data)}
    >
      <Image
        src={isWarning ? "/images/bell.png" : "/images/calendar.png"}
        alt="icon"
        width={28}
        height={28}
        style={{
          filter: data.is_read ? "grayscale(100%)" : "none",
        }}
      />
      <div className="flex-1 gap-2">
        <p
          style={{
            fontWeight: "bold",
            margin: 0,
            color: data.is_read ? "rgb(160,160,160)" : "rgb(217, 161, 50)",
          }}
        >
          {data.message}
        </p>
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
            color: data.is_read ? "rgb(160,160,160)" : "rgb(217, 161, 50)",
          }}
        >
          <FieldTimeOutlined />
          <p style={{ margin: 0 }}>
            {dayjs(data.created_at).diff(dayjs(), "day") > 0
              ? `${dayjs(data.created_at).diff(dayjs(), "day")} ngày trước`
              : `${dayjs(data.created_at).format("DD/MM/YYYY HH:mm")}`}
          </p>
        </div>
      </div>
    </div>
  );
};

export default function NotificationPopover() {
  const { client } = useMqttStore();

  const [notiData, setNotiData] = useState<NoticeDataRes[]>([]);
  const [searchedNotiData, setSearchedNotiData] = useState<NoticeDataRes[]>([]);
  const [NotiDataWillBeShown, setNotiDataWillBeShown] = useState<
    NoticeDataRes[]
  >([]);

  useEffect(() => {
    async function fetchData() {
      const res = await getWebNotificationListAll();
      setNotiData(res);
      setSearchedNotiData(res);
    }
    fetchData();
  }, []);

  const [typeOfFilterNotiData, setTypeOfFilterNotiData] = useState<
    "all" | "unseen" | "seen"
  >("all");

  const options: CheckboxGroupProps<string>["options"] = [
    { label: <p>Tất cả</p>, value: "all" },
    {
      label: (
        <p>
          <EyeInvisibleOutlined />
          &nbsp;Chưa xem
        </p>
      ),
      value: "unseen",
    },
    {
      label: (
        <p>
          <EyeOutlined />
          &nbsp;Đã xem
        </p>
      ),
      value: "seen",
    },
  ];

  const handleSearchNotiData = (value: string) => {
    if (!value) {
      setSearchedNotiData(notiData);
      return;
    }
    const searchedData = notiData.filter((item) =>
      item.message.toLowerCase().includes(value)
    );
    setSearchedNotiData(searchedData);
  };

  useEffect(() => {
    if (typeOfFilterNotiData === "all") {
      setNotiDataWillBeShown(searchedNotiData);
    } else if (typeOfFilterNotiData === "unseen") {
      const filteredData = searchedNotiData?.filter(
        (item) => item.is_read === false || item.is_read === 0
      );
      setNotiDataWillBeShown(filteredData);
    } else if (typeOfFilterNotiData === "seen") {
      const filteredData = searchedNotiData?.filter(
        (item) => item.is_read === true || item.is_read === 1
      );
      setNotiDataWillBeShown(filteredData);
    }
  }, [typeOfFilterNotiData, searchedNotiData]);

  useEffect(() => {
    client.on("message", (topic, msg) => {
      if (topic !== genNoticeUpdateTopic()) return;
      try {
        console.log("WEB NOTIFICATION: ", JSON.parse(msg.toString()));
      } catch (error) {
        console.log("error: ", error);
      }
    });
  }, []);

  // const [openDrawer, setOpenDrawer] = useState(false);
  // const handleShowSpecificProgramSchedule = (program_id: string) => {
  //   setOpenDrawer(true);
  // };

  const content = (
    <div
      style={{
        width: 400,
        height: 450,
        display: "flex",
        flexDirection: "column",
        gap: 8,
      }}
    >
      <InputTextWithKeyboard
        suffix={<SearchOutlined />}
        placeholder="Tìm kiếm thông báo"
        onChange={(e) =>
          handleSearchNotiData(e.target.value as "all" | "unseen" | "seen")
        }
      />
      <Radio.Group
        block
        options={options}
        value={typeOfFilterNotiData}
        onChange={(e) => setTypeOfFilterNotiData(e.target.value)}
        optionType="button"
        buttonStyle="solid"
      />
      <div
        style={{
          width: "100%",
          height: 400,
          overflowY: "scroll",
          display: "flex",
          flexDirection: "column",
          gap: 8,
        }}
      >
        {NotiDataWillBeShown?.map((item) => (
          <AlarmItem
            key={item.name}
            data={item}
            setNotiData={setNotiData}
            setSearchedNotiData={setSearchedNotiData}
            setNotiDataWillBeShown={setNotiDataWillBeShown}
            // handleShowSpecificProgramSchedule={handleShowSpecificProgramSchedule}
          />
        ))}
      </div>
    </div>
  );
  return (
    <div>
      <Popover content={content} title="Thông báo" trigger="click">
        <Button
          icon={
            <NotificationOutlined
              style={{
                padding: 4,
                color: "#45c3a1",
                fontSize: 16,
              }}
            />
          }
        />
      </Popover>
      {/* <Drawer
      title="Chi tiết chương trình"
      placement={"right"}
      onClose={() => setOpenDrawer(false)}
      open={openDrawer}
      width={'70%'}
    >
      <DetailedProgram
        program={program}
        onClose={() => {
          setOpenDrawerToEditProgram(false);
        }}
        start_date_of_plan={start_date_of_plan}
        end_date_of_plan={end_date_of_plan}
      />
    </Drawer> */}
    </div>
  );
}
