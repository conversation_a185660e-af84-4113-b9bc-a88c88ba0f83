import { FC, useEffect, useState } from "react";
import {
  FunctionList,
  getLatestDataDevices,
} from "../../services/device/devices";
import { generateAPIPath } from "../../services/utilities";
import { DashboardOutlined } from "@ant-design/icons";
import { genDeviceTopic } from "../../stores/mqttStore.utils";
import { useMqttStore } from "../../stores/mqttStore";
import useDeviceDataStore from "../../stores/deviceDataStore";
import { MqttNoticeDataSub } from "../../events/mqtt/mqtt-device-eventemitter";

interface ChartMonitorProps {
  dataFunction?: FunctionList;
  deviceKey: string;
}

const Unchart: FC<ChartMonitorProps> = ({ dataFunction, deviceKey }) => {
  const [currentValue, setCurrentValue] = useState<string | number>(0);
  const { deviceId, deviceData } = useDeviceDataStore();
  const { subscribe, unsubscribe, handleMessage, client } = useMqttStore();

  useEffect(() => {
    console.log("deviceKey:", deviceKey);
    console.log("deviceId:", deviceId);
    console.log("dataFunction:", dataFunction);
    if (!deviceId || !dataFunction?.identifier) return;

    async function getLatestDataFromHTTPAPI() {
      const res = await getLatestDataDevices({
        deviceId: deviceId,
        keys: [dataFunction.identifier],
      });
      const dataValue = res?.data?.[dataFunction.identifier]?.[0];
      if (dataValue) {
        const valueAsNumber = parseFloat(dataValue.value);
        setCurrentValue(isNaN(valueAsNumber) ? "" : valueAsNumber.toFixed(2));
      }
    }
    getLatestDataFromHTTPAPI();

    client.on("message", (topic, msg) => {
      if (topic !== genDeviceTopic(deviceId)) return;
      console.log(
        `UN CHART: Received MQTT message on topic ${topic}:`,
        msg.toString()
      );
      try {
        const data: Array<{ ts: string; key: string; value: string | number }> =
          JSON.parse(msg.toString());
        if (Array.isArray(data)) {
          const deviceData = data.filter(
            (d) => d.key === dataFunction.identifier
          );
          if (deviceData.length > 0) {
            const latestData = deviceData[deviceData.length - 1];
            const valueAsNumber = parseFloat(String(latestData.value));
            setCurrentValue(
              isNaN(valueAsNumber) ? "" : valueAsNumber.toFixed(2)
            );
          }
        }
      } catch (error) {
        console.error("MQTT message error:", error);
      }
    });
  }, [deviceId, dataFunction?.identifier]);

  return (
    <div
      style={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        gap: 8,
        border: "1px solid rgb(230,230,230)",
        padding: 8,
        borderRadius: 8,
        backgroundColor: "#fff",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
        }}
      >
        {dataFunction?.icon_url ? (
          <img
            height={"24px"}
            src={generateAPIPath(
              "api/v2/file/download?file_url=" + dataFunction.icon_url
            )}
            onError={() => <DashboardOutlined />}
          />
        ) : (
          <DashboardOutlined />
        )}
        <p style={{ margin: 0, color: "rgb(100,100,100)" }}>
          {dataFunction?.label}
        </p>
      </div>
      <p style={{ fontWeight: "bold", fontSize: "16px", margin: 0 }}>
        {currentValue} {dataFunction?.unit}
      </p>
    </div>
  );
};

export default Unchart;
