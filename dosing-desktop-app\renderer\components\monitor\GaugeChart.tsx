import React, { useEffect, useState } from "react";
import {
  FunctionList,
  getLatestDataDevices,
} from "../../services/device/devices";
import { Progress } from "antd";
import { useMqttStore } from "../../stores/mqttStore";
import { genDeviceTopic } from "../../stores/mqttStore.utils";
import useDeviceDataStore from "../../stores/deviceDataStore";
import { MqttNoticeDataSub } from "../../events/mqtt/mqtt-device-eventemitter";

interface ChartMonitorProps {
  dataFunction?: FunctionList;
  deviceKey: string;
}

const GaugeChart: React.FC<ChartMonitorProps> = ({
  deviceKey,
  dataFunction,
}) => {
  const min = dataFunction?.data_measure_min || 0;
  const max = dataFunction?.data_measure_max || 100;

  const [currentValue, setCurrentValue] = useState<number>(0);
  const { deviceId } = useDeviceDataStore();
  const { subscribe, unsubscribe, handleMessage, client } = useMqttStore();
  const { deviceData } = useDeviceDataStore();

  useEffect(() => {
    if (!deviceId || !dataFunction?.identifier) return;

    async function getLatestDataFromHTTPAPI() {
      const res = await getLatestDataDevices({
        deviceId: deviceId,
        keys: [dataFunction.identifier],
      });
      const dataValue = res?.data?.[dataFunction.identifier]?.[0];
      if (dataValue) {
        const valueAsNumber = parseFloat(dataValue.value);
        setCurrentValue(
          isNaN(valueAsNumber) ? 0 : parseFloat(valueAsNumber.toFixed(2))
        );
      }
    }
    getLatestDataFromHTTPAPI();

    client.on("message", (topic, msg) => {
      if (topic !== genDeviceTopic(deviceId)) return;
      console.log(
        `GAUGE CHART: Received MQTT message on topic ${topic}:`,
        msg.toString()
      );
      try {
        const data: Array<{ ts: string; key: string; value: string | number }> =
          JSON.parse(msg.toString());
        if (Array.isArray(data)) {
          const deviceData = data.filter(
            (d) => d.key === dataFunction.identifier
          );
          if (deviceData.length > 0) {
            const latestData = deviceData[deviceData.length - 1];
            const valueAsNumber = parseFloat(String(latestData.value));
            setCurrentValue(
              isNaN(valueAsNumber) ? 0 : parseFloat(valueAsNumber.toFixed(2))
            );
          }
        }
      } catch (error) {
        console.error("MQTT message error:", error);
      }
    });
  }, [deviceId, dataFunction?.identifier]);

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        alignItems: "flex-end",
        justifyContent: "center",
      }}
    >
      <p style={{ color: "gray" }}>{min}</p>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <p style={{ fontSize: "16px", fontWeight: "bold" }}>
          {dataFunction?.label || "Untitled"}
        </p>
        <Progress
          type="dashboard"
          steps={48}
          percent={((currentValue - min) / (max - min)) * 100}
          trailColor="rgba(0, 0, 0, 0.06)"
          strokeWidth={10}
          format={(percent) => (
            <div
              style={{
                color:
                  ((currentValue - min) / (max - min)) * 100 < 50
                    ? "#45c3a1"
                    : ((currentValue - min) / (max - min)) * 100 < 75
                    ? "#FF8C00"
                    : "red",
              }}
            >
              <p style={{ fontSize: "16px", fontWeight: "bold" }}>
                {currentValue}
              </p>
              <p style={{ fontSize: "14px", margin: 0 }}>
                {dataFunction?.unit}
              </p>
            </div>
          )}
          strokeColor={
            ((currentValue - min) / (max - min)) * 100 < 50
              ? "#45c3a1"
              : ((currentValue - min) / (max - min)) * 100 < 75
              ? "#FF8C00"
              : "red"
          }
          // strokeColor={{
          //   "0%": "#45c3a1",
          //   "50%": "#45c3a1",
          //   "100%": "red",
          // }}
          status="active"
        />
      </div>
      <p style={{ color: "gray" }}>{max}</p>
    </div>
  );
};

export default GaugeChart;
