import { FC, useEffect, useState } from "react";
import {
  ScheduleAction,
  ScheduleProgram,
} from "../../../stores/schedulePlanStore";
import { Col, Drawer, message, Row } from "antd";
import EnableProgram from "./Update/EnableProgram";
import useDeviceDataStore from "../../../stores/deviceDataStore";
import { FunctionList } from "../../../services/device/devices";
import DeleteProgram from "./Update/DeleteProgram";
import DetailedProgram from "./Detail/DetailedProgram";
import dayjs from "dayjs";

export const calculateSecond = (start_time: string, end_time: string) => {
  const today = new Date().toISOString().split("T")[0]; // e.g., "2025-05-22"
  const startDate = new Date(`${today}T${start_time}`);
  const endDate = new Date(`${today}T${end_time}`);

  // Calculate difference in seconds
  const diffInSeconds = (endDate.getTime() - startDate.getTime()) / 1000;

  return diffInSeconds;
};

const generateADayInWeek = (day: string, program_enable: boolean) => {
  return (
    <div
      style={{
        height: "40px",
        borderRadius: 16,
        border: "1px solid #ddd",
        padding: 8,
        background: program_enable ? "#45c3a1" : "#ddd",
      }}
    >
      <p style={{ margin: 0, color: "white", fontWeight: "bold" }}>{day}</p>
    </div>
  );
};

const generateIntervalWeekdays = (
  interval: string,
  program_enable: boolean
) => {
  const days = interval.split(",");
  return days.map((day) => {
    switch (day) {
      case "0":
        return generateADayInWeek("Chủ nhật", program_enable);
      case "1":
        return generateADayInWeek("Thứ 2", program_enable);
      case "2":
        return generateADayInWeek("Thứ 3", program_enable);
      case "3":
        return generateADayInWeek("Thứ 4", program_enable);
      case "4":
        return generateADayInWeek("Thứ 5", program_enable);
      case "5":
        return generateADayInWeek("Thứ 6", program_enable);
      case "6":
        return generateADayInWeek("Thứ 7", program_enable);
      default:
        return "";
    }
  });
};

interface ProgramContainerProps {
  program: ScheduleProgram;
  start_date_of_plan: string;
  end_date_of_plan: string;
}

const ProgramContainer: FC<ProgramContainerProps> = ({
  program,
  start_date_of_plan,
  end_date_of_plan,
}) => {
  const [programActivated, setProgramActivated] = useState(
    program.enable ? 1 : 0
  );

  const { functionListForControl } = useDeviceDataStore();
  console.log("functionListForControl: ", functionListForControl);

  const generateActionsOfPlan = (action: ScheduleAction) => {
    const fncControl = functionListForControl.find(
      (item) => item.identifier === "tb1"
    )?.children;
    // let newValue = {
    //   on: '',
    //   off: '',
    // }
    const lableAndUnit = {
      label: "",
      unit: "",
    };

    const generateActionNameAndUnit = (key: string) => {
      fncControl?.forEach(
        (item: { label: string; children: FunctionList[] }) => {
          item?.children?.forEach((child: FunctionList) => {
            if (child?.identifier === key) {
              lableAndUnit.label = child?.label;
              lableAndUnit.unit = child?.unit;
              // newValue = {
              //   on: child?.data_on_text,
              //   off: child?.data_off_text,
              // };
            }
          });
        }
      );
      return lableAndUnit;
    };

    return (
      <div style={{ width: "100%", display: "flex", flexDirection: "column" }}>
        {Object.entries(action).map(([key, value]) => {
          if (typeof value !== "number" && String(value) !== "true") {
            return null;
          }
          if (typeof value === "number" && value === 0) {
            return null;
          }

          return (
            <div
              key={key}
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                gap: 8,
              }}
            >
              <p style={{ margin: 0 }}>
                {generateActionNameAndUnit(key).label}
              </p>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  width: "100px",
                }}
              >
                <p
                  style={{
                    margin: 0,
                    fontWeight: value !== 0 ? "bold" : "normal",
                  }}
                >
                  {value === "false" ? "Tắt" : value === "true" ? "Bật" : value}
                  {/* {newValue} */}
                </p>
                <p style={{ margin: 0 }}>
                  {lableAndUnit.unit}
                  {/* {key.toLowerCase().includes("ec")
                    ? "mS/cm"
                    : key.toLowerCase().includes("flow")
                    ? "ml/phút"
                    : key.toLowerCase().includes("holding")
                    ? "Giây"
                    : ""} */}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const [openDrawerToEditProgram, setOpenDrawerToEditProgram] =
    useState<boolean>(false);

  return (
    <div
      key={program.id}
      style={{
        display: "flex",
        flexDirection: "column",
        boxShadow: "0px 0px 8px rgba(0, 0, 0, 0.1)",
        border: "1px solid #eee",
        padding: 8,
        borderRadius: 16,
        background: programActivated
          ? "linear-gradient(to bottom left,rgb(152, 251, 158), 5%, #fff)"
          : "#fff",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
          }}
        >
          <div
            style={{
              height: 8,
              width: 8,
              borderRadius: "50%",
              backgroundColor: programActivated ? "#45c3a1" : "gray",
            }}
          ></div>
          <p
            style={{
              fontSize: 16,
              fontWeight: "bold",
              margin: 0,
              color: programActivated ? "#45c3a1" : "gray",
              cursor: "pointer",
            }}
            onClick={() => {
              if (programActivated === 0) {
                setOpenDrawerToEditProgram(true);
              } else {
                message.error(
                  "Chương trình này đang được kích hoạt ! Vui lòng tắt trước"
                );
              }
            }}
          >
            {program.name || "..."}
          </p>
        </div>
        <DeleteProgram
          plan_id={program.schedule_plan_id}
          program_id={program.id}
          enable_program={program.enable}
        />
      </div>

      <div
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
          marginTop: 16,
          marginBottom: 16,
        }}
      >
        <div
          style={{
            margin: 0,
            color: programActivated ? "#45c3a1" : "gray",
            fontSize: 13,
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            gap: 8,
          }}
        >
          <p style={{ margin: 0 }}> Lịch thực hiện: </p>
          {generateIntervalWeekdays(
            program.interval,
            programActivated === 0 ? false : true
          ).map((day) => day)}
        </div>
        <EnableProgram
          program={program}
          plan_id={program.schedule_plan_id}
          programActivated={programActivated ? 1 : 0}
          setProgramActivated={setProgramActivated}
        />
      </div>

      <div
        style={{
          color: programActivated ? "#45c3a1" : "gray",
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Row gutter={[32, 32]}>
          <Col
            span={12}
            style={{
              display: "flex",
              flexDirection: "row",
              gap: 32,
              // justifyContent: "space-between",
            }}
          >
            <p style={{ margin: 0 }}>Thời điểm:</p>
            <p style={{ margin: 0, fontWeight: "bold" }}>
              {/* {program.start_time.split(":")[0]}:
              {program.start_time.split(":")[1]}&nbsp;-&nbsp;
              {program.end_time.split(":")[0]}:{program.end_time.split(":")[1]} */}
              {dayjs(program.start_time, "HH:mm:ss").format("HH:mm:ss")} -{" "}
              {dayjs(program.end_time, "HH:mm:ss").format("HH:mm:ss")}
            </p>
          </Col>
          <Col
            span={12}
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <p style={{ margin: 0 }}>Tổng thời gian: </p>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
                width: "100px",
              }}
            >
              <p style={{ margin: 0, fontWeight: "bold" }}>
                {calculateSecond(program.start_time, program.end_time)}
              </p>
              <p style={{ margin: 0 }}>Giây</p>
            </div>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={24}>
            <p>
              Mã môi trường: &nbsp;&nbsp;{" "}
              <strong>{program?.action?.env_enum || ""}</strong>
            </p>
          </Col>
        </Row>

        <div
          style={{
            width: "100%",
            borderBottom: "1px solid #eee",
            marginTop: 8,
            marginBottom: 16,
          }}
        ></div>
        {generateActionsOfPlan(program.action)}
      </div>

      <Drawer
        title="Chỉnh sửa chương trình"
        open={openDrawerToEditProgram}
        onClose={() => {
          setOpenDrawerToEditProgram(false);
        }}
        width={"70%"}
      >
        <DetailedProgram
          program={program}
          onClose={() => {
            setOpenDrawerToEditProgram(false);
          }}
          start_date_of_plan={start_date_of_plan}
          end_date_of_plan={end_date_of_plan}
        />
      </Drawer>

      <div style={{ width: "100%", height: 80 }}></div>
    </div>
  );
};

export default ProgramContainer;
