import { <PERSON>Complete, Button, Form, Image, Input, message, Row } from "antd";
import FormItem from "antd/es/form/FormItem";
// import InputTextWithKeyboard from "../../components/virtual-input/InputTextWithKeyboard";
import { getListAllValidUsers, login } from "../../services/auth";
import useUserStore from "../../stores/userStore";
import { useEffect, useState } from "react";
import useLanguageStore from "../../stores/languageStore";
import InputTextWithKeyboard from "../../components/virtual-input/InputTextWithKeyboard";

export const LoginPage = () => {
  const languageData = useLanguageStore((state) => state.languageData);
  const setEmail = useUserStore((state) => state.setEmail);

  // this functionality for selecting users from a list of users in the database
  const [listOfAllValidUsers, setListOfAllValidUsers] = useState<any[]>([]); // State to store the list of users
  useEffect(() => {
    async function fetchUsers() {
      try {
        const response = await getListAllValidUsers();
        setListOfAllValidUsers(response?.responseData?.data);
      } catch (error) {
        message.error(
          "Có lỗi xảy ra khi lấy thông tin người dùng! Vui lòng thử lại sau."
        );
      }
    }

    fetchUsers();
  }, []);

  const [listUserOptions, setListUserOptions] = useState<
    { label: React.ReactNode; value: string }[]
  >([]);
  useEffect(() => {
    console.log("listOfAllValidUsers: ", listOfAllValidUsers);
    if (!listOfAllValidUsers || listOfAllValidUsers?.length === 0) return;
    const tmpList = listOfAllValidUsers.map((user) => ({
      label: (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 4,
            borderBottom: "1px solid #ddd",
          }}
        >
          <p style={{ fontWeight: "bold", margin: 0 }}>
            {user?.last_name + " " + user?.first_name}
          </p>
          <p style={{ color: "rgb(100,100,100)", margin: 0 }}>{user?.email}</p>
        </div>
      ),
      value: user?.email,
    }));
    setListUserOptions(tmpList);
  }, [listOfAllValidUsers]);

  const handleSubmit = async (values: { usr: string; pwd: string }) => {
    try {
      await login({ ...values }).then((res) => {
        localStorage.setItem(
          "token",
          JSON.stringify(res?.responseData?.result)
        );
        setEmail(res?.responseData?.result?.user?.email);
      });
    } catch (error) {
      console.error("Login error:", error);
      message.error(languageData["common.login.error"]);
    }
  };

  return (
    <Row
      justify="center"
      align="top"
      className="center-top-screen flex flex-col"
    >
      <Form
        style={{
          width: "400px",
          // border: "1px solid #eee",
          padding: "20px",
          borderRadius: "10px",
          backgroundColor: "#fff",
          marginBottom: "20px",
        }}
        onFinish={handleSubmit}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Image
            src="/images/Vietplants-logo.png"
            width={100}
            preview={false}
          />
          <p
            style={{
              marginTop: "20px",
              fontSize: "24px",
              fontWeight: "bold",
            }}
          >
            Đăng nhập
          </p>
        </div>
        <FormItem name="usr">
          {/* <InputTextWithKeyboard placeholder="Tài khoản" size="large" /> */}
          {/* <Input placeholder="Tài khoản" size="large" /> */}
          <AutoComplete
            options={listUserOptions}
            // style={{ width: 300 }}
            size="large"
            // value={}
            // onChange={(data) => setValue(data)}
            placeholder="Chọn hoặc nhập tài khoản"
            filterOption={(inputValue, option) =>
              option?.value.toLowerCase().includes(inputValue.toLowerCase())
            }
          />
        </FormItem>
        <FormItem name="pwd">
          <InputTextWithKeyboard
            placeholder="Mật khẩu"
            size="large"
            type="password"
          />
          {/* <Input placeholder="Mật khẩu" size="large" type="password" /> */}
        </FormItem>
        <Button
          type="primary"
          htmlType="submit"
          style={{ width: "100%" }}
          size="large"
        >
          Login
        </Button>
      </Form>
      <Button
        disabled
        title="Tạm thời chưa hỗ trợ lấy lại mật khẩu"
        type="link"
        href="/user/forgot-password"
      >
        Forgot password?
      </Button>
      <p
        style={{
          marginTop: "30px",
          textAlign: "center",
          color: "rgb(100,100,100)",
        }}
      >
        {" "}
        &#169;2025 Powered by VIIS{" "}
      </p>
    </Row>
  );
};

export default LoginPage;

export const ForgotPasswordPage = () => {
  return (
    <Row
      justify="center"
      align="top"
      className="center-top-screen flex flex-col"
    >
      <Form
        style={{
          width: "400px",
          padding: "20px",
          borderRadius: "10px",
          backgroundColor: "#fff",
          marginBottom: "20px",
        }}
        // onFinish={handleSubmit}
      >
        <p
          style={{ fontSize: "20px", fontWeight: "bold", marginBottom: "40px" }}
        >
          Quên mật khẩu
        </p>
        <FormItem name="username">
          {/* <InputTextWithKeyboard placeholder="Email người dùng" size="large" /> */}
          <Input placeholder="Email người dùng" size="large" />
        </FormItem>
        <Button
          type="primary"
          htmlType="submit"
          style={{ width: "100%" }}
          size="large"
        >
          Lấy lại mật khẩu
        </Button>
      </Form>
      <a href="/user/login">Quay về trang đăng nhập</a>
      <p
        style={{
          marginTop: "30px",
          textAlign: "center",
          color: "rgb(100,100,100)",
        }}
      >
        {" "}
        &#169; 2025 Powered by VIIS{" "}
      </p>
    </Row>
  );
};
